# PowerShell script to launch the extension
Write-Host "Compiling TypeScript..." -ForegroundColor Green
npm run compile

if ($LASTEXITCODE -eq 0) {
    Write-Host "Compilation successful!" -ForegroundColor Green
    Write-Host "Launching VS Code Extension Development Host..." -ForegroundColor Yellow
    
    # Launch VS Code with extension development path
    & code --extensionDevelopmentPath="$PWD" --new-window
    
    Write-Host "Extension Development Host should be starting..." -ForegroundColor Green
    Write-Host "Look for the chat icon in the Activity Bar (left sidebar)" -ForegroundColor Cyan
} else {
    Write-Host "Compilation failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
