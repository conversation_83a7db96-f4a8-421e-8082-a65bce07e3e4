"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
function activate(context) {
    // Register the chat view provider
    const provider = new ChatViewProvider(context.extensionUri);
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('aiChatView', provider));
    // Register the command to open chat
    const openChatCommand = vscode.commands.registerCommand('aiChat.openChat', () => {
        vscode.commands.executeCommand('workbench.view.extension.aiChatContainer');
    });
    context.subscriptions.push(openChatCommand);
}
exports.activate = activate;
class ChatViewProvider {
    constructor(_extensionUri) {
        this._extensionUri = _extensionUri;
    }
    resolveWebviewView(webviewView, context, _token) {
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(message => {
            switch (message.command) {
                case 'getApiKey':
                    const config = vscode.workspace.getConfiguration();
                    const apiKey = config.get('openrouter.apiKey');
                    const model = config.get('openrouter.model');
                    webviewView.webview.postMessage({
                        command: 'apiKey',
                        key: apiKey,
                        model: model
                    });
                    break;
                case 'openSettings':
                    vscode.commands.executeCommand('workbench.action.openSettings', 'openrouter');
                    break;
            }
        }, undefined);
    }
    _getHtmlForWebview(webview) {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Assistant</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: var(--vscode-font-family);
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            font-size: var(--vscode-font-size);
        }
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        .chat-history {
            flex-grow: 1;
            padding: 10px;
            overflow-y: auto;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        .chat-message {
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 6px;
            max-width: 90%;
            word-wrap: break-word;
            line-height: 1.4;
            font-size: 13px;
        }
        .user-message {
            background-color: var(--vscode-inputOption-activeBackground);
            align-self: flex-end;
            margin-left: auto;
            border: 1px solid var(--vscode-inputOption-activeBorder);
        }
        .bot-message {
            background-color: var(--vscode-input-background);
            align-self: flex-start;
            border: 1px solid var(--vscode-input-border);
        }
        .system-message {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            font-style: italic;
            align-self: center;
            max-width: 95%;
            text-align: center;
            font-size: 12px;
        }
        .chat-input {
            display: flex;
            padding: 10px;
            background-color: var(--vscode-editor-background);
            border-top: 1px solid var(--vscode-panel-border);
        }
        .chat-input textarea {
            flex-grow: 1;
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            color: var(--vscode-input-foreground);
            padding: 8px;
            resize: none;
            outline: none;
            font-family: var(--vscode-font-family);
            font-size: 13px;
            min-height: 32px;
            max-height: 100px;
        }
        .chat-input textarea:focus {
            border-color: var(--vscode-focusBorder);
        }
        .chat-input textarea:disabled {
            background-color: var(--vscode-input-background);
            opacity: 0.6;
            cursor: not-allowed;
        }
        .chat-input button {
            margin-left: 8px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            height: fit-content;
            align-self: flex-end;
        }
        .chat-input button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .chat-input button:disabled {
            background-color: var(--vscode-button-secondaryBackground);
            cursor: not-allowed;
            opacity: 0.6;
        }
        .loading-indicator {
            display: none;
            text-align: center;
            padding: 8px;
        }
        .loader {
            border: 2px solid var(--vscode-progressBar-background);
            border-top: 2px solid var(--vscode-progressBar-background);
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .settings-link {
            color: var(--vscode-textLink-foreground);
            text-decoration: underline;
            cursor: pointer;
        }
        .settings-link:hover {
            color: var(--vscode-textLink-activeForeground);
        }
        .message-content {
            white-space: pre-wrap;
        }
        .model-display {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            text-align: center;
            padding: 4px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="model-display" id="model-display">Loading...</div>
        <div class="chat-history" id="chat-history">
            <!-- Messages will be added here -->
        </div>
        <div class="loading-indicator" id="loading-indicator">
            <div class="loader"></div>
        </div>
        <div class="chat-input">
            <textarea id="message-input" placeholder="Waiting for API Key..." rows="1" disabled></textarea>
            <button id="send-button" disabled>Send</button>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        const chatHistory = document.getElementById('chat-history');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const loadingIndicator = document.getElementById('loading-indicator');
        const modelDisplay = document.getElementById('model-display');

        let openRouterApiKey = '';
        let currentModel = 'moonshotai/kimi-k2';
        let conversationHistory = [];

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });

        // Function to add a message to the chat history
        function addMessage(text, type, isHtml = false) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('chat-message', \`\${type}-message\`);

            if (isHtml) {
                messageElement.innerHTML = text;
            } else {
                const contentDiv = document.createElement('div');
                contentDiv.classList.add('message-content');
                contentDiv.textContent = text;
                messageElement.appendChild(contentDiv);
            }

            chatHistory.appendChild(messageElement);
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }

        // Function to handle sending a message
        async function sendMessage() {
            const text = messageInput.value.trim();
            if (text === '' || !openRouterApiKey) return;

            addMessage(text, 'user');
            conversationHistory.push({ role: 'user', content: text });

            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendButton.disabled = true;
            messageInput.disabled = true;
            loadingIndicator.style.display = 'block';

            try {
                const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
                    method: "POST",
                    headers: {
                        "Authorization": \`Bearer \${openRouterApiKey}\`,
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        "model": currentModel,
                        "messages": conversationHistory.slice(-10) // Keep last 10 messages for context
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    const errorMessage = errorData.error?.message || \`HTTP status \${response.status}\`;
                    throw new Error(\`API Error: \${errorMessage}\`);
                }

                const data = await response.json();
                const botReply = data.choices[0].message.content;
                addMessage(botReply, 'bot');
                conversationHistory.push({ role: 'assistant', content: botReply });

            } catch (error) {
                console.error('Error calling OpenRouter:', error);
                addMessage(\`Error: \${error.message}\`, 'system');
            } finally {
                sendButton.disabled = false;
                messageInput.disabled = false;
                loadingIndicator.style.display = 'none';
                messageInput.focus();
            }
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);

        messageInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        });

        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'apiKey':
                    currentModel = message.model || 'moonshotai/kimi-k2';
                    modelDisplay.textContent = \`Model: \${currentModel}\`;

                    if (message.key && message.key !== 'YOUR_OPENROUTER_API_KEY' && message.key.trim() !== '') {
                        openRouterApiKey = message.key;
                        messageInput.disabled = false;
                        sendButton.disabled = false;
                        messageInput.placeholder = "Ask me anything...";
                        addMessage("🤖 AI Chat Assistant is ready! How can I help you today?", "system");
                    } else {
                        const settingsMessage = \`OpenRouter API key not found. Please set it in VS Code settings. <span class="settings-link" onclick="openSettings()">Open Settings</span>\`;
                        addMessage(settingsMessage, 'system', true);
                        messageInput.placeholder = "API Key required - check settings";
                        modelDisplay.textContent = "API Key Required";
                    }
                    break;
            }
        });

        function openSettings() {
            vscode.postMessage({ command: 'openSettings' });
        }

        // Initialize chat
        function initializeChat() {
            chatHistory.innerHTML = '';
            addMessage("Initializing AI Chat Assistant...", "system");
            vscode.postMessage({ command: 'getApiKey' });
        }

        // Save and restore state
        const state = vscode.getState();
        if (state && state.conversationHistory) {
            conversationHistory = state.conversationHistory;
            chatHistory.innerHTML = state.chatHtml || '';
        }

        window.addEventListener('beforeunload', () => {
            vscode.setState({
                conversationHistory: conversationHistory,
                chatHtml: chatHistory.innerHTML
            });
        });

        initializeChat();
    </script>
</body>
</html>`;
    }
}
ChatViewProvider.viewType = 'aiChatView';
function deactivate() { }
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map