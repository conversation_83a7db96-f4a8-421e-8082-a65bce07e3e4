@echo off
echo.
echo ========================================
echo   AI Chat Assistant Extension Launcher
echo ========================================
echo.
echo Compiling extension...
call npm run compile
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo Compilation successful!
echo.
echo Launching VS Code Extension Development Host...
echo Look for the chat icon in the Activity Bar (left sidebar)
echo.

start "" code --extensionDevelopmentPath="%cd%" --new-window

echo.
echo Extension Development Host should be starting...
echo If you don't see the chat icon, check the console for errors.
echo.
pause
