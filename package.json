{"name": "ai-chat-assistant", "displayName": "AI Chat Assistant", "description": "AI-powered chat interface using OpenRouter API", "version": "1.0.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onCommand:aiChat.openChat"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "aiChat.openChat", "title": "Open AI Chat", "category": "AI Chat"}], "configuration": {"title": "AI Chat Assistant", "properties": {"openrouter.apiKey": {"type": "string", "default": "", "description": "Your OpenRouter API key", "scope": "application"}, "openrouter.model": {"type": "string", "default": "moonshotai/kimi-k2", "description": "The AI model to use", "enum": ["moonshotai/kimi-k2", "x-ai/grok-4", "qwen/qwen3-coder", "anthropic/claude-sonnet-4", "anthropic/claude-3.7-sonnet", "anthropic/claude-3.7-sonnet:thinking", "google/gemini-flash-1.5", "google/gemini-pro-1.5", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "openai/gpt-4-turbo", "openai/gpt-3.5-turbo"]}}}, "viewsContainers": {"activitybar": [{"id": "aiChatContainer", "title": "AI Chat", "icon": "$(comment-discussion)"}]}, "views": {"aiChatContainer": [{"id": "aiChatView", "name": "Chat Assistant", "type": "webview"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}