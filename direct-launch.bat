@echo off
echo.
echo ==========================================
echo   Direct Launch - AI Chat Assistant
echo ==========================================
echo.
echo Compiling extension...
call npm run compile
if %errorlevel% neq 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo Launching VS Code Extension Development Host...
echo.
echo IMPORTANT: Look for the chat icon in the Activity Bar!
echo.

start "" code --extensionDevelopmentPath="%cd%" --new-window --disable-extensions

echo.
echo Extension Development Host launched!
echo.
echo What to do next:
echo 1. Look for the chat icon (💬) in the left Activity Bar
echo 2. Click it to open the AI Chat Assistant
echo 3. You should see "AI Chat Assistant Ready!" immediately
echo 4. The input field should be active and ready for typing
echo.
pause
