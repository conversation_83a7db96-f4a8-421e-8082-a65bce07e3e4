{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,kCAAkC;IAClC,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAE5D,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,YAAY,EAAE,QAAQ,CAAC,CACpE,CAAC;IAEF,oCAAoC;IACpC,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC5E,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,0CAA0C,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAChD,CAAC;AAdD,4BAcC;AAED,MAAM,gBAAgB;IAGlB,YAA6B,aAAyB;QAAzB,kBAAa,GAAb,aAAa,CAAY;IAAG,CAAC;IAEnD,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;SAC3C,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAExE,mCAAmC;QACnC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,WAAW;oBACZ,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;oBACnD,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAW,CAAC;oBACzD,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAW,CAAC;oBAEvD,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;wBAC5B,OAAO,EAAE,QAAQ;wBACjB,GAAG,EAAE,MAAM;wBACX,KAAK,EAAE,KAAK;qBACf,CAAC,CAAC;oBACH,MAAM;gBACV,KAAK,cAAc;oBACf,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,YAAY,CAAC,CAAC;oBAC9E,MAAM;aACb;QACL,CAAC,EACD,SAAS,CACZ,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAC9C,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAkzBP,CAAC;IACL,CAAC;;AA51BsB,yBAAQ,GAAG,YAAY,CAAC;AA+1BnD,SAAgB,UAAU,KAAI,CAAC;AAA/B,gCAA+B"}