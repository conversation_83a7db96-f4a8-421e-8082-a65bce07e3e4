{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,kCAAkC;IAClC,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAE5D,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,YAAY,EAAE,QAAQ,CAAC,CACpE,CAAC;IAEF,oCAAoC;IACpC,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC5E,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,0CAA0C,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAChD,CAAC;AAdD,4BAcC;AAED,MAAM,gBAAgB;IAGlB,YAA6B,aAAyB;QAAzB,kBAAa,GAAb,aAAa,CAAY;IAAG,CAAC;IAEnD,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;SAC3C,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAExE,sDAAsD;QACtD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE3C,mCAAmC;QACnC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,WAAW;oBACZ,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAC1C,MAAM;gBACV,KAAK,cAAc;oBACf,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,YAAY,CAAC,CAAC;oBAC9E,MAAM;gBACV,KAAK,OAAO;oBACR,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC9C,MAAM;gBACV,KAAK,OAAO;oBACR,sCAAsC;oBACtC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAC1C,MAAM;aACb;QACL,CAAC,EACD,SAAS,CACZ,CAAC;IACN,CAAC;IAEO,gBAAgB,CAAC,OAAuB;QAC5C,gCAAgC;QAChC,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAEO,eAAe,CAAC,OAAuB;QAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;QACnD,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAW,CAAC;QACvD,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAW,CAAC;QAErD,qEAAqE;QACrE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACjC,MAAM,GAAG,2EAA2E,CAAC;YACrF,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/B,KAAK,GAAG,oBAAoB,CAAC;SAChC;QAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE;YAC9C,SAAS,EAAE,CAAC,CAAC,MAAM;YACnB,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACxC,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU;SACpE,CAAC,CAAC;QAEH,OAAO,CAAC,WAAW,CAAC;YAChB,OAAO,EAAE,QAAQ;YACjB,GAAG,EAAE,MAAM;YACX,KAAK,EAAE,KAAK;YACZ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAC9C,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAyjCP,CAAC;IACL,CAAC;;AA1oCsB,yBAAQ,GAAG,YAAY,CAAC;AA6oCnD,SAAgB,UAAU,KAAI,CAAC;AAA/B,gCAA+B"}