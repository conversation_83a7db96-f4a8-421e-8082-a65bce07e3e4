import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    // Register the chat view provider
    const provider = new ChatViewProvider(context.extensionUri);
    
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('aiChatView', provider)
    );

    // Register the command to open chat
    const openChatCommand = vscode.commands.registerCommand('aiChat.openChat', () => {
        vscode.commands.executeCommand('workbench.view.extension.aiChatContainer');
    });

    context.subscriptions.push(openChatCommand);
}

class ChatViewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'aiChatView';

    constructor(private readonly _extensionUri: vscode.Uri) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'getApiKey':
                        const config = vscode.workspace.getConfiguration();
                        const apiKey = config.get('openrouter.apiKey') as string;
                        const model = config.get('openrouter.model') as string;

                        console.log('Extension: Getting API key...', { apiKey: apiKey ? 'SET' : 'NOT_SET', model });

                        webviewView.webview.postMessage({
                            command: 'apiKey',
                            key: apiKey || '',
                            model: model || 'moonshotai/kimi-k2'
                        });
                        break;
                    case 'openSettings':
                        vscode.commands.executeCommand('workbench.action.openSettings', 'openrouter');
                        break;
                    case 'debug':
                        console.log('Extension Debug:', message.data);
                        break;
                }
            },
            undefined
        );
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Assistant</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--vscode-editor-background) 0%, var(--vscode-sideBar-background) 100%);
            color: var(--vscode-editor-foreground);
            font-size: 14px;
            line-height: 1.6;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            position: relative;
        }

        .chat-header {
            background: linear-gradient(90deg, var(--vscode-titleBar-activeBackground) 0%, var(--vscode-button-background) 100%);
            padding: 16px 20px;
            border-bottom: 2px solid var(--vscode-panel-border);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .model-display {
            font-size: 13px;
            font-weight: 600;
            color: var(--vscode-button-foreground);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .model-icon {
            width: 16px;
            height: 16px;
            background: var(--vscode-button-foreground);
            border-radius: 50%;
            display: inline-block;
        }

        .chat-history {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            scroll-behavior: smooth;
            background: var(--vscode-editor-background);
        }

        .chat-history::-webkit-scrollbar {
            width: 6px;
        }

        .chat-history::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-history::-webkit-scrollbar-thumb {
            background: var(--vscode-scrollbarSlider-background);
            border-radius: 3px;
        }

        .chat-history::-webkit-scrollbar-thumb:hover {
            background: var(--vscode-scrollbarSlider-hoverBackground);
        }

        .message-wrapper {
            margin-bottom: 24px;
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }

        .message-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }

        .user-avatar {
            background: linear-gradient(135deg, #007ACC 0%, #005A9E 100%);
        }

        .bot-avatar {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
        }

        .system-avatar {
            background: linear-gradient(135deg, #6C757D 0%, #495057 100%);
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
        }

        .chat-message {
            padding: 16px 20px;
            border-radius: 16px;
            max-width: 85%;
            word-wrap: break-word;
            line-height: 1.5;
            font-size: 14px;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .chat-message:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .user-message {
            background: linear-gradient(135deg, var(--vscode-button-background) 0%, var(--vscode-button-hoverBackground) 100%);
            color: var(--vscode-button-foreground);
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }

        .bot-message {
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            margin-right: auto;
            border-bottom-left-radius: 4px;
        }

        .system-message {
            background: linear-gradient(135deg, var(--vscode-badge-background) 0%, var(--vscode-button-secondaryBackground) 100%);
            color: var(--vscode-badge-foreground);
            font-style: italic;
            margin: 0 auto;
            max-width: 90%;
            text-align: center;
            font-size: 13px;
            border-radius: 20px;
        }

        .message-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .chat-message:hover .message-actions {
            opacity: 1;
        }

        .copy-btn {
            background: var(--vscode-button-secondaryBackground);
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
            color: var(--vscode-button-secondaryForeground);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .copy-btn:hover {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }

        .chat-input-container {
            background: var(--vscode-sideBar-background);
            border-top: 2px solid var(--vscode-panel-border);
            padding: 20px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }

        .chat-input {
            display: flex;
            gap: 12px;
            align-items: flex-end;
            background: var(--vscode-input-background);
            border: 2px solid var(--vscode-input-border);
            border-radius: 12px;
            padding: 12px;
            transition: all 0.2s ease;
        }

        .chat-input:focus-within {
            border-color: var(--vscode-focusBorder);
            box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
        }

        .chat-input textarea {
            flex-grow: 1;
            background: transparent;
            border: none;
            color: var(--vscode-input-foreground);
            resize: none;
            outline: none;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            min-height: 20px;
            max-height: 120px;
        }

        .chat-input textarea::placeholder {
            color: var(--vscode-input-placeholderForeground);
            opacity: 0.7;
        }

        .send-button {
            background: linear-gradient(135deg, var(--vscode-button-background) 0%, var(--vscode-button-hoverBackground) 100%);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .send-button:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
        }

        .send-button:disabled {
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            cursor: not-allowed;
            opacity: 0.6;
        }
        .loading-indicator {
            display: none;
            padding: 20px;
            text-align: center;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 16px;
            margin-right: auto;
            max-width: 200px;
            animation: messageSlideIn 0.3s ease-out;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: var(--vscode-button-background);
            border-radius: 50%;
            animation: typingPulse 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes typingPulse {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .settings-link {
            color: var(--vscode-textLink-foreground);
            text-decoration: none;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .settings-link:hover {
            color: var(--vscode-textLink-activeForeground);
            background: var(--vscode-button-secondaryBackground);
        }

        .message-content {
            white-space: pre-wrap;
            word-break: break-word;
        }

        .code-block {
            background: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            position: relative;
        }

        .code-block::before {
            content: attr(data-language);
            position: absolute;
            top: 4px;
            right: 8px;
            font-size: 10px;
            color: var(--vscode-descriptionForeground);
            text-transform: uppercase;
        }

        .error-message {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.1) 100%);
            border: 1px solid rgba(255, 107, 53, 0.3);
            color: var(--vscode-errorForeground);
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: var(--vscode-descriptionForeground);
        }

        .welcome-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.7;
        }

        .model-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: statusPulse 2s infinite;
        }

        @keyframes statusPulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="model-display" id="model-display">
                <div class="model-icon"></div>
                <span>Loading...</span>
                <div class="status-indicator" id="status-indicator" style="display: none;"></div>
            </div>
        </div>

        <div class="chat-history" id="chat-history">
            <div class="welcome-message" id="welcome-message">
                <div class="welcome-icon">🤖</div>
                <h3>AI Chat Assistant</h3>
                <p>Initializing your AI assistant...</p>
            </div>
        </div>

        <div class="loading-indicator" id="loading-indicator">
            <div class="typing-indicator">
                <div class="message-avatar bot-avatar">AI</div>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="chat-input">
                <textarea id="message-input" placeholder="Ask me anything..." rows="1" disabled></textarea>
                <button class="send-button" id="send-button" disabled>
                    <span>Send</span>
                    <span>⏎</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        const chatHistory = document.getElementById('chat-history');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const loadingIndicator = document.getElementById('loading-indicator');
        const modelDisplay = document.getElementById('model-display');
        const statusIndicator = document.getElementById('status-indicator');
        const welcomeMessage = document.getElementById('welcome-message');

        let openRouterApiKey = '';
        let currentModel = 'moonshotai/kimi-k2';
        let conversationHistory = [];
        let messageCount = 0;

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });

        // Function to add a message to the chat history
        function addMessage(text, type, isHtml = false) {
            // Hide welcome message on first real message
            if (welcomeMessage && (type === 'user' || type === 'bot')) {
                welcomeMessage.style.display = 'none';
            }

            const messageWrapper = document.createElement('div');
            messageWrapper.classList.add('message-wrapper');

            // Create message header with avatar and timestamp
            const messageHeader = document.createElement('div');
            messageHeader.classList.add('message-header');

            const avatar = document.createElement('div');
            avatar.classList.add('message-avatar');

            const timestamp = document.createElement('span');
            timestamp.classList.add('message-time');
            timestamp.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            if (type === 'user') {
                avatar.classList.add('user-avatar');
                avatar.textContent = 'You';
                messageHeader.appendChild(avatar);
                messageHeader.appendChild(document.createTextNode('You'));
                messageHeader.appendChild(timestamp);
            } else if (type === 'bot') {
                avatar.classList.add('bot-avatar');
                avatar.textContent = 'AI';
                messageHeader.appendChild(avatar);
                messageHeader.appendChild(document.createTextNode('AI Assistant'));
                messageHeader.appendChild(timestamp);
            } else {
                avatar.classList.add('system-avatar');
                avatar.textContent = '⚙️';
                messageHeader.appendChild(avatar);
                messageHeader.appendChild(document.createTextNode('System'));
                messageHeader.appendChild(timestamp);
            }

            // Create message content
            const messageElement = document.createElement('div');
            messageElement.classList.add('chat-message', \`\${type}-message\`);

            // Add copy button for user and bot messages
            if (type === 'user' || type === 'bot') {
                const messageActions = document.createElement('div');
                messageActions.classList.add('message-actions');

                const copyBtn = document.createElement('button');
                copyBtn.classList.add('copy-btn');
                copyBtn.textContent = 'Copy';
                copyBtn.onclick = () => {
                    navigator.clipboard.writeText(text);
                    copyBtn.textContent = 'Copied!';
                    setTimeout(() => copyBtn.textContent = 'Copy', 2000);
                };

                messageActions.appendChild(copyBtn);
                messageElement.appendChild(messageActions);
            }

            if (isHtml) {
                messageElement.innerHTML += text;
            } else {
                const contentDiv = document.createElement('div');
                contentDiv.classList.add('message-content');

                // Simple code block detection and highlighting
                if (text.includes('\`\`\`')) {
                    contentDiv.innerHTML = formatCodeBlocks(text);
                } else {
                    contentDiv.textContent = text;
                }

                messageElement.appendChild(contentDiv);
            }

            messageWrapper.appendChild(messageHeader);
            messageWrapper.appendChild(messageElement);
            chatHistory.appendChild(messageWrapper);
            chatHistory.scrollTop = chatHistory.scrollHeight;

            messageCount++;
        }

        // Function to format code blocks
        function formatCodeBlocks(text) {
            return text.replace(/\`\`\`(\w+)?\n([\s\S]*?)\`\`\`/g, (match, language, code) => {
                return \`<div class="code-block" data-language="\${language || 'code'}">\${escapeHtml(code.trim())}</div>\`;
            }).replace(/\`([^\`]+)\`/g, '<code>$1</code>');
        }

        // Function to escape HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Function to handle sending a message
        async function sendMessage() {
            const text = messageInput.value.trim();
            if (text === '' || !openRouterApiKey) return;

            // Add user message with animation
            addMessage(text, 'user');
            conversationHistory.push({ role: 'user', content: text });

            // Reset input
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Show loading state
            sendButton.disabled = true;
            messageInput.disabled = true;
            loadingIndicator.style.display = 'block';

            // Update send button to show loading
            const sendButtonSpan = sendButton.querySelector('span');
            const originalText = sendButtonSpan.textContent;
            sendButtonSpan.textContent = 'Sending...';

            try {
                const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
                    method: "POST",
                    headers: {
                        "Authorization": \`Bearer \${openRouterApiKey}\`,
                        "Content-Type": "application/json",
                        "HTTP-Referer": "https://vscode-ai-chat.local",
                        "X-Title": "VS Code AI Chat Assistant"
                    },
                    body: JSON.stringify({
                        "model": currentModel,
                        "messages": conversationHistory.slice(-20), // Keep last 20 messages for context
                        "temperature": 0.7,
                        "max_tokens": 4000
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    const errorMessage = errorData.error?.message || \`HTTP \${response.status}: \${response.statusText}\`;
                    throw new Error(errorMessage);
                }

                const data = await response.json();
                const botReply = data.choices[0].message.content;

                // Add bot response with animation
                setTimeout(() => {
                    addMessage(botReply, 'bot');
                    conversationHistory.push({ role: 'assistant', content: botReply });
                }, 300); // Small delay for better UX

            } catch (error) {
                console.error('Error calling OpenRouter:', error);
                const errorMsg = document.createElement('div');
                errorMsg.innerHTML = \`
                    <div style="color: var(--vscode-errorForeground); font-weight: 600;">❌ Error</div>
                    <div style="margin-top: 8px;">\${error.message}</div>
                    <div style="margin-top: 8px; font-size: 12px; opacity: 0.8;">
                        Please check your API key and try again.
                        <span class="settings-link" onclick="openSettings()">Open Settings</span>
                    </div>
                \`;
                addMessage(errorMsg.innerHTML, 'system', true);
            } finally {
                // Reset UI state
                setTimeout(() => {
                    sendButton.disabled = false;
                    messageInput.disabled = false;
                    loadingIndicator.style.display = 'none';
                    sendButtonSpan.textContent = originalText;
                    messageInput.focus();
                }, 300);
            }
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);

        messageInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        });

        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            console.log('Webview: Received message:', message);

            switch (message.command) {
                case 'apiKey':
                    currentModel = message.model || 'moonshotai/kimi-k2';
                    console.log('Webview: Processing API key...', {
                        hasKey: !!message.key,
                        keyLength: message.key ? message.key.length : 0,
                        model: currentModel
                    });

                    // Update model display with modern styling
                    const modelSpan = modelDisplay.querySelector('span');
                    if (modelSpan) {
                        modelSpan.textContent = \`\${getModelDisplayName(currentModel)}\`;
                    }

                    if (message.key && message.key.trim() !== '' && message.key !== 'YOUR_OPENROUTER_API_KEY') {
                        openRouterApiKey = message.key;
                        console.log('Webview: API key set successfully');

                        // Enable interface
                        messageInput.disabled = false;
                        sendButton.disabled = false;
                        messageInput.placeholder = "Ask me anything...";
                        if (statusIndicator) statusIndicator.style.display = 'block';

                        // Update welcome message
                        if (welcomeMessage) {
                            welcomeMessage.innerHTML = \`
                                <div class="welcome-icon">🤖</div>
                                <h3>AI Chat Assistant Ready!</h3>
                                <p>Connected to <strong>\${getModelDisplayName(currentModel)}</strong></p>
                                <p style="font-size: 13px; opacity: 0.8;">Start a conversation by typing a message below.</p>
                                <div style="margin-top: 16px; font-size: 11px; opacity: 0.6;">
                                    API Key: ✓ Connected | Model: \${currentModel}
                                </div>
                            \`;
                        }

                        // Focus the input
                        setTimeout(() => messageInput.focus(), 100);

                    } else {
                        console.log('Webview: No valid API key found');
                        // Show API key required state
                        if (statusIndicator) statusIndicator.style.display = 'none';

                        const settingsMessage = \`
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 32px; margin-bottom: 16px;">🔑</div>
                                <h3>API Key Required</h3>
                                <p>Please configure your OpenRouter API key to start chatting.</p>
                                <div style="margin-top: 16px;">
                                    <button onclick="showApiKeyInput()" style="
                                        margin: 4px; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer;
                                        background: var(--vscode-button-background); color: var(--vscode-button-foreground);
                                    ">Enter API Key</button>
                                    <button onclick="openSettings()" style="
                                        margin: 4px; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer;
                                        background: var(--vscode-button-secondaryBackground); color: var(--vscode-button-secondaryForeground);
                                    ">Open Settings</button>
                                </div>
                                <div style="margin-top: 16px; font-size: 11px; opacity: 0.6;">
                                    Debug: Key received = \${message.key ? 'YES' : 'NO'} | Length = \${message.key ? message.key.length : 0}
                                </div>
                            </div>
                        \`;

                        if (welcomeMessage) {
                            welcomeMessage.innerHTML = settingsMessage;
                        }

                        messageInput.placeholder = "API Key required - check settings";
                        if (modelSpan) modelSpan.textContent = "API Key Required";
                    }
                    break;
            }
        });

        // Function to get display name for models
        function getModelDisplayName(model) {
            const modelNames = {
                'moonshotai/kimi-k2': 'Kimi K2',
                'x-ai/grok-4': 'Grok 4',
                'qwen/qwen3-coder': 'Qwen 3 Coder',
                'anthropic/claude-sonnet-4': 'Claude Sonnet 4',
                'anthropic/claude-3.7-sonnet': 'Claude 3.7 Sonnet',
                'anthropic/claude-3.7-sonnet:thinking': 'Claude 3.7 (Thinking)',
                'google/gemini-flash-1.5': 'Gemini Flash 1.5',
                'google/gemini-pro-1.5': 'Gemini Pro 1.5',
                'anthropic/claude-3-sonnet': 'Claude 3 Sonnet',
                'anthropic/claude-3-haiku': 'Claude 3 Haiku',
                'openai/gpt-4-turbo': 'GPT-4 Turbo',
                'openai/gpt-3.5-turbo': 'GPT-3.5 Turbo'
            };
            return modelNames[model] || model;
        }

        function openSettings() {
            vscode.postMessage({ command: 'openSettings' });
        }

        // Manual API key input function
        function showApiKeyInput() {
            if (welcomeMessage) {
                welcomeMessage.innerHTML = \`
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 32px; margin-bottom: 16px;">🔑</div>
                        <h3>Enter API Key</h3>
                        <p>Paste your OpenRouter API key below:</p>
                        <input type="password" id="manual-api-key" placeholder="sk-or-v1-..." style="
                            width: 100%;
                            max-width: 300px;
                            padding: 8px 12px;
                            margin: 12px 0;
                            border: 1px solid var(--vscode-input-border);
                            border-radius: 4px;
                            background: var(--vscode-input-background);
                            color: var(--vscode-input-foreground);
                        ">
                        <div style="margin-top: 12px;">
                            <button onclick="setManualApiKey()" style="
                                padding: 8px 16px;
                                margin-right: 8px;
                                border: none;
                                border-radius: 6px;
                                cursor: pointer;
                                background: var(--vscode-button-background);
                                color: var(--vscode-button-foreground);
                            ">Set API Key</button>
                            <button onclick="openSettings()" style="
                                padding: 8px 16px;
                                border: none;
                                border-radius: 6px;
                                cursor: pointer;
                                background: var(--vscode-button-secondaryBackground);
                                color: var(--vscode-button-secondaryForeground);
                            ">Open Settings</button>
                        </div>
                    </div>
                \`;
            }
        }

        function setManualApiKey() {
            const input = document.getElementById('manual-api-key');
            if (input && input.value.trim()) {
                openRouterApiKey = input.value.trim();
                currentModel = 'moonshotai/kimi-k2';

                // Enable interface
                messageInput.disabled = false;
                sendButton.disabled = false;
                messageInput.placeholder = "Ask me anything...";
                if (statusIndicator) statusIndicator.style.display = 'block';

                // Update model display
                const modelSpan = modelDisplay.querySelector('span');
                if (modelSpan) {
                    modelSpan.textContent = getModelDisplayName(currentModel);
                }

                // Update welcome message
                if (welcomeMessage) {
                    welcomeMessage.innerHTML = \`
                        <div class="welcome-icon">🤖</div>
                        <h3>AI Chat Assistant Ready!</h3>
                        <p>Connected to <strong>\${getModelDisplayName(currentModel)}</strong></p>
                        <p style="font-size: 13px; opacity: 0.8;">Start a conversation by typing a message below.</p>
                        <div style="margin-top: 16px; font-size: 11px; opacity: 0.6;">
                            API Key: ✓ Manually Set | Model: \${currentModel}
                        </div>
                    \`;
                }

                // Focus the input
                setTimeout(() => messageInput.focus(), 100);
            }
        }

        // Enhanced auto-resize for textarea
        function autoResizeTextarea() {
            messageInput.style.height = 'auto';
            const newHeight = Math.min(messageInput.scrollHeight, 120);
            messageInput.style.height = newHeight + 'px';
        }

        // Initialize chat with fade-in animation
        function initializeChat() {
            console.log('Webview: Initializing chat...');
            document.body.classList.add('fade-in');

            // Send debug info
            vscode.postMessage({
                command: 'debug',
                data: 'Chat initialization started'
            });

            // Request API key
            console.log('Webview: Requesting API key...');
            vscode.postMessage({ command: 'getApiKey' });

            // Set a timeout to show error if no response
            setTimeout(() => {
                if (!openRouterApiKey && welcomeMessage) {
                    console.log('Webview: No API key received after 5 seconds');
                    welcomeMessage.innerHTML = \`
                        <div style="text-align: center; padding: 20px;">
                            <div style="font-size: 32px; margin-bottom: 16px;">⚠️</div>
                            <h3>Initialization Issue</h3>
                            <p>Unable to load API key. Please check your settings.</p>
                            <button class="settings-link" onclick="openSettings()" style="margin-top: 12px; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer;">
                                Open Settings
                            </button>
                            <div style="margin-top: 16px; font-size: 12px; opacity: 0.7;">
                                Debug: Extension communication timeout
                            </div>
                        </div>
                    \`;
                }
            }, 5000);
        }

        // Enhanced state management
        const state = vscode.getState();
        if (state && state.conversationHistory && state.conversationHistory.length > 0) {
            conversationHistory = state.conversationHistory;
            messageCount = state.messageCount || 0;

            // Restore messages with proper formatting
            if (state.messages) {
                welcomeMessage.style.display = 'none';
                state.messages.forEach(msg => {
                    addMessage(msg.content, msg.type, msg.isHtml);
                });
            }
        }

        // Save state more comprehensively
        function saveState() {
            const messages = Array.from(chatHistory.querySelectorAll('.message-wrapper')).map(wrapper => {
                const messageEl = wrapper.querySelector('.chat-message');
                const type = messageEl.classList.contains('user-message') ? 'user' :
                           messageEl.classList.contains('bot-message') ? 'bot' : 'system';
                const content = messageEl.querySelector('.message-content')?.textContent || messageEl.textContent;
                const isHtml = messageEl.innerHTML !== messageEl.textContent;
                return { content, type, isHtml };
            });

            vscode.setState({
                conversationHistory: conversationHistory,
                messageCount: messageCount,
                messages: messages,
                currentModel: currentModel
            });
        }

        // Auto-save state periodically
        setInterval(saveState, 5000);

        window.addEventListener('beforeunload', saveState);

        // Enhanced keyboard shortcuts
        messageInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter') {
                if (event.shiftKey) {
                    // Allow new line with Shift+Enter
                    return;
                } else {
                    event.preventDefault();
                    if (!sendButton.disabled) {
                        sendMessage();
                    }
                }
            } else if (event.key === 'Escape') {
                messageInput.blur();
            }
        });

        // Focus management
        messageInput.addEventListener('focus', () => {
            document.querySelector('.chat-input').style.borderColor = 'var(--vscode-focusBorder)';
        });

        messageInput.addEventListener('blur', () => {
            document.querySelector('.chat-input').style.borderColor = 'var(--vscode-input-border)';
        });

        // Initialize
        initializeChat();
    </script>
</body>
</html>`;
    }
}

export function deactivate() {}
