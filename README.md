# AI Chat Assistant for VS Code

A powerful VS Code extension that provides an AI-powered chat interface using the OpenRouter API. Chat with various AI models directly within your VS Code environment.

## Features

- 🤖 **Multiple AI Models**: Support for various AI models including:
  - Moonshot AI Kimi K2 (default)
  - X.AI Grok 4
  - Qwen 3 Coder
  - Anthropic Claude Sonnet 4
  - Anthropic Claude 3.7 Sonnet
  - Anthropic Claude 3.7 Sonnet (Thinking)
  - Google Gemini Flash 1.5
  - Google Gemini Pro 1.5
  - Anthropic Claude 3 Sonnet
  - Anthropic Claude 3 Haiku
  - OpenAI GPT-4 Turbo
  - OpenAI GPT-3.5 Turbo

- 💬 **Modern Chat Interface**: Beautiful, responsive chat interface with smooth animations
- 👤 **Message Avatars & Timestamps**: Professional message layout with user avatars and timestamps
- 📋 **Copy Functionality**: One-click copy for any message with visual feedback
- 🎨 **Syntax Highlighting**: Automatic code block detection and formatting
- 🔄 **Smart Conversation History**: Maintains context with intelligent message management
- ⚡ **Enhanced UX**: Typing indicators, loading states, and micro-interactions
- ⚙️ **Easy Configuration**: Simple settings management through VS Code preferences
- 🎨 **VS Code Theme Integration**: Automatically adapts to your VS Code theme with modern gradients
- 🚀 **Smooth Animations**: Fade-in effects, hover states, and polished transitions
- ♿ **Accessibility**: Keyboard shortcuts, focus management, and screen reader support

## Installation

### From Source

1. Clone or download this repository
2. Open the folder in VS Code
3. Install dependencies:
   ```bash
   npm install
   ```
4. Compile the extension:
   ```bash
   npm run compile
   ```
5. Press `F5` to run the extension in a new Extension Development Host window

### Manual Installation

1. Package the extension:
   ```bash
   vsce package
   ```
2. Install the generated `.vsix` file in VS Code

## Setup

1. **Get an OpenRouter API Key**:
   - Visit [OpenRouter.ai](https://openrouter.ai/)
   - Sign up for an account
   - Generate an API key

2. **Configure the Extension**:
   - Open VS Code Settings (`Ctrl+,` or `Cmd+,`)
   - Search for "openrouter"
   - Set your API key in `OpenRouter: Api Key`
   - Optionally change the model in `OpenRouter: Model`

## Usage

1. **Open the Chat Interface**:
   - Click the chat icon in the Activity Bar (left sidebar)
   - Or use the Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`) and run "Open AI Chat"

2. **Start Chatting**:
   - Type your message in the input field
   - Press `Enter` to send (or `Shift+Enter` for new line)
   - The AI will respond in the chat history

3. **Features**:
   - **Modern Interface**: Beautiful message bubbles with avatars and timestamps
   - **Copy Messages**: Click the copy button on any message to copy to clipboard
   - **Code Highlighting**: Automatic syntax highlighting for code blocks
   - **Smart Input**: Auto-resizing input field with keyboard shortcuts
   - **Conversation History**: Persistent chat history across sessions
   - **Loading States**: Elegant typing indicators and loading animations
   - **Error Handling**: User-friendly error messages with helpful suggestions
   - **Keyboard Shortcuts**:
     - `Enter` to send message
     - `Shift+Enter` for new line
     - `Escape` to unfocus input

## Configuration Options

| Setting | Description | Default |
|---------|-------------|---------|
| `openrouter.apiKey` | Your OpenRouter API key | `""` |
| `openrouter.model` | AI model to use | `"moonshotai/kimi-k2"` |

## Available Models

- `moonshotai/kimi-k2` - Moonshot AI's Kimi K2 model (default)
- `x-ai/grok-4` - X.AI's Grok 4 model
- `qwen/qwen3-coder` - Qwen 3 specialized for coding
- `anthropic/claude-sonnet-4` - Latest Claude Sonnet 4
- `anthropic/claude-3.7-sonnet` - Claude 3.7 Sonnet
- `anthropic/claude-3.7-sonnet:thinking` - Claude 3.7 with thinking mode
- `google/gemini-flash-1.5` - Fast and efficient
- `google/gemini-pro-1.5` - More capable, slower
- `anthropic/claude-3-sonnet` - Balanced performance
- `anthropic/claude-3-haiku` - Fast and cost-effective
- `openai/gpt-4-turbo` - Most capable OpenAI model
- `openai/gpt-3.5-turbo` - Fast and cost-effective

## Development

### Building

```bash
npm run compile
```

### Watching for Changes

```bash
npm run watch
```

### Running the Extension

Press `F5` in VS Code to launch a new Extension Development Host window with the extension loaded.

## Troubleshooting

### "API Key not found" Error
- Make sure you've set your OpenRouter API key in VS Code settings
- Restart VS Code after setting the API key

### "API Error" Messages
- Check that your API key is valid
- Ensure you have credits/quota available on your OpenRouter account
- Try switching to a different model

### Extension Not Loading
- Make sure the extension is compiled (`npm run compile`)
- Check the VS Code Developer Console for errors (`Help > Toggle Developer Tools`)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

If you encounter any issues or have feature requests, please create an issue in the repository.
